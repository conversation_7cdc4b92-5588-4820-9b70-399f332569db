<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Infrastructure;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard as LeaderboardConfig;
use OpenLoyalty\Leaderboard\Domain\Factory\LeaderboardFactoryInterface;
use OpenLoyalty\Leaderboard\Domain\LeaderboardGateInterface;
use OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface;

final readonly class LeaderboardGate implements LeaderboardGateInterface
{
    public function __construct(
        private LeaderboardRepositoryInterface $leaderboardRepository,
        private LeaderboardFactoryInterface $leaderboardFactory
    ) {
    }

    public function createLeaderboardForCampaign(Campaign $campaign, LeaderboardConfig $leaderboardConfig): void
    {
        $leaderboard = $this->leaderboardFactory->createFromCampaign($campaign, $leaderboardConfig);
        
        $this->leaderboardRepository->save($leaderboard);
    }
}
