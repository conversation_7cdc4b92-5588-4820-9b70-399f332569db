<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Infrastructure;

use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Leaderboard\Domain\Factory\LeaderboardFactoryInterface;
use OpenLoyalty\Leaderboard\Domain\LeaderboardGateInterface;
use OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface;
use DateTimeImmutable;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

final readonly class LeaderboardGate implements LeaderboardGateInterface
{
    public function __construct(
        private LeaderboardRepositoryInterface $leaderboardRepository,
        private LeaderboardFactoryInterface $leaderboardFactory
    ) {
    }

    public function createLeaderboardForCampaign(
        CampaignId $campaignId,
        StoreId $storeId,
        \DateTimeInterface $startsAt,
        ?\DateTimeInterface $endsAt,
        Leaderboard $leaderboardConfig
    ): void {
        $leaderboard = $this->leaderboardFactory->create(
            $campaignId,
            $storeId,
            DateTimeImmutable::createFromInterface($startsAt),
            $endsAt ? \DateTimeImmutable::createFromInterface($endsAt) : null,
            new Metric($leaderboardConfig->getMetric()->type, $leaderboardConfig->getMetric()->walletTypeCode)
        );
        
        $this->leaderboardRepository->save($leaderboard);
    }
}
