OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard:
    type: entity
    table: leaderboard
    id:
        leaderboardId:
            type: leaderboard_id
    cache:
        usage: NONSTRICT_READ_WRITE
        region: campaign
    fields:
        storeId:
            type: store_id
            nullable: false
        campaignId:
            type: campaign_id
            nullable: false
        iterPeriod:
            type: string
            nullable: false
        periodFrom:
            type: string
            nullable: false
        periodTo:
            type: string
            nullable: false
        recalculatedAt:
            type: datetime_immutable_microseconds
        completedAt:
            type: datetime_immutable_microseconds
    embedded:
        metric:
            class: OpenLoyalty\Leaderboard\Domain\ValueObject\Metric
    uniqueConstraints:
        leaderboardReadModelIdx:
            columns: [ store_id, leaderboard_id, iter_period ]