OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard:
    type: entity
    table: leaderboard
    id:
        leaderboardId:
            type: leaderboard_id
    cache:
        usage: NONSTRICT_READ_WRITE
        region: campaign
    fields:
        storeId:
            type: store_id
            nullable: false
        campaignId:
            type: campaign_id
            nullable: false
        firstIterPeriod:
            type: string
            nullable: false
        previousIterPeriod:
            type: string
            nullable: false
        currentIterPeriod:
            type: string
            nullable: false
    embedded:
        metric:
            class: OpenLoyalty\Leaderboard\Domain\ValueObject\Metric
    uniqueConstraints:
        leaderboardReadModelIdx:
            columns: [ store_id, leaderboard_id, iter_period ]