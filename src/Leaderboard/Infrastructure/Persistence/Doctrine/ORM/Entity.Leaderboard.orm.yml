OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard:
    type: entity
    table: leaderboard
    id:
        leaderboardId:
            type: leaderboard_id
    indexes:
        leaderboardStoreIdx:
            columns: [ store_id ]
        leaderboardCurrentIterPeriodIdx:
            columns: [ iter_period_current_iter_period ]
    cache:
        usage: NONSTRICT_READ_WRITE
        region: campaign
    fields:
        storeId:
            type: store_id
            nullable: false
    embedded:
        metric:
            class: OpenLoyalty\Leaderboard\Domain\ValueObject\Metric
        iterPeriod:
            class: OpenLoyalty\Leaderboard\Domain\ValueObject\IterPeriod