<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Infrastructure\Persistence\Doctrine\Repository;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Infrastructure\Persistence\Doctrine\Repository\DoctrineRepository;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;
use OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface;

final class LeaderboardRepository extends DoctrineRepository implements LeaderboardRepositoryInterface
{
    protected function getClass(): string
    {
        return Leaderboard::class;
    }

    public function byId(LeaderboardId $leaderboardId): ?Leaderboard
    {
        return $this->find($leaderboardId);
    }

    public function save(Leaderboard $leaderboard): void
    {
        $this->entityManager->persist($leaderboard);
        $this->entityManager->flush();
    }

    public function byCampaignId(CampaignId $campaignId): ?Leaderboard
    {
        return $this->findOneBy(['campaignId' => $campaignId]);
    }
}
