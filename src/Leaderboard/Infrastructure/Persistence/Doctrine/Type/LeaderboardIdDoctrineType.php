<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Infrastructure\Persistence\Doctrine\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\GuidType;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;

final class LeaderboardIdType extends GuidType
{
    public const NAME = 'leaderboard_id';

    public function convertToPHPValue($value, AbstractPlatform $platform): ?LeaderboardId
    {
        if ($value === null) {
            return null;
        }

        if ($value instanceof LeaderboardId) {
            return $value;
        }

        return new LeaderboardId($value);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof LeaderboardId) {
            return (string) $value;
        }

        if (!empty($value)) {
            return $value;
        }

        return null;
    }

    public function getName(): string
    {
        return self::NAME;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform): bool
    {
        return true;
    }
}
