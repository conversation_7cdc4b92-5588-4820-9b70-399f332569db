services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    OpenLoyalty\Leaderboard\Infrastructure\:
        resource: '../../*'
        exclude:
            - '../../{DependencyInjection,Resources,OpenLoyaltyLeaderboardBundle.php}'

    OpenLoyalty\Leaderboard\Domain\:
        resource: '../../../Domain'
        exclude:
            - '../../../Domain/{ValueObject,Exception,Entity,DTO}'

    OpenLoyalty\Leaderboard\Application\UseCase\:
        resource: '../../../Application/UseCase'

    OpenLoyalty\Leaderboard\Application\DataMapper\:
        resource: '../../../Application/DataMapper'

    OpenLoyalty\Leaderboard\Infrastructure\LeaderboardGate: ~
    OpenLoyalty\Leaderboard\Domain\LeaderboardGateInterface: '@OpenLoyalty\Leaderboard\Infrastructure\LeaderboardGate'

    OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface:
        alias: OpenLoyalty\Leaderboard\Infrastructure\Persistence\Doctrine\Repository\LeaderboardRepository

    OpenLoyalty\Leaderboard\Ui\Rest\Controller\:
        resource: '../../../Ui/Rest/Controller'
        tags: [ 'controller.service_arguments' ]
