services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    OpenLoyalty\Leaderboard\Infrastructure\:
        resource: '../../*'
        exclude:
            - '../../{DependencyInjection,Resources,OpenLoyaltyLeaderboardBundle.php}'

    OpenLoyalty\Leaderboard\Domain\:
        resource: '../../../Domain'
        exclude:
            - '../../../Domain/{ValueObject,Exception,Entity,DTO}'

    OpenLoyalty\Leaderboard\Application\UseCase\:
        resource: '../../../Application/UseCase'

    OpenLoyalty\Leaderboard\Application\DataMapper\:
        resource: '../../../Application/DataMapper'

    OpenLoyalty\Leaderboard\Application\Facade\:
        resource: '../../../Application/Facade'

    OpenLoyalty\Leaderboard\Domain\Facade\LeaderboardFacadeInterface:
        alias: OpenLoyalty\Leaderboard\Application\Facade\LeaderboardFacade

    OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface:
        alias: OpenLoyalty\Leaderboard\Infrastructure\Persistence\Doctrine\Repository\LeaderboardRepository

    OpenLoyalty\Leaderboard\Ui\Rest\Controller\:
        resource: '../../../Ui/Rest/Controller'
        tags: [ 'controller.service_arguments' ]
