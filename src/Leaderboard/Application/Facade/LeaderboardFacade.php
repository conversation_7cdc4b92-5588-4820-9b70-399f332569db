<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Application\Facade;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard as LeaderboardConfig;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;
use OpenLoyalty\Leaderboard\Domain\Facade\LeaderboardFacadeInterface;
use OpenLoyalty\Leaderboard\Domain\LeaderboardRepositoryInterface;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

final readonly class LeaderboardFacade implements LeaderboardFacadeInterface
{
    public function __construct(
        private LeaderboardRepositoryInterface $leaderboardRepository,
        private UuidGeneratorInterface $uuidGenerator
    ) {
    }

    public function createLeaderboardForCampaign(Campaign $campaign, LeaderboardConfig $leaderboardConfig): void
    {
        $metric = new Metric(
            $leaderboardConfig->getMetric()->getType(),
            $leaderboardConfig->getMetric()->getWalletTypeCode()
        );

        $leaderboard = new Leaderboard(
            new LeaderboardId($this->uuidGenerator->generate()),
            'monthly', // iterPeriod - domyślnie miesięczny
            $campaign->getStartsAt()->format('Y-m-d'), // periodFrom
            $campaign->getEndsAt()?->format('Y-m-d') ?? '2099-12-31', // periodTo
            $campaign->getStore(),
            $campaign,
            $metric
        );

        $this->leaderboardRepository->save($leaderboard);
    }
}
