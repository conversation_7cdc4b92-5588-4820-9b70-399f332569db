<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Entity;

use DateTimeImmutable;
use DateTimeInterface;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Leaderboard\Domain\Service\PeriodProviderInterface;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

class Leaderboard
{
    private LeaderboardId $leaderboardId;
    private string $firstIterPeriod;
    private string $preciousIterPeriod;
    private string $currentIterPeriod;
    private ?DateTimeImmutable $recalculatedAt = null;
    private ?DateTimeImmutable $completedAt = null;
    private StoreId $storeId;
    private CampaignId $campaignId;
    private Metric $metric;

    private function __construct(
        LeaderboardId $leaderboardId,
        string $firstIterPeriod,
        string $previousIterPeriod,
        string $currentIterPeriod,
        StoreId $storeId,
        CampaignId $campaignId,
        Metric $metric
    ) {
        $this->leaderboardId = $leaderboardId;
        $this->firstIterPeriod = $firstIterPeriod;
        $this->preciousIterPeriod = $previousIterPeriod;
        $this->currentIterPeriod = $currentIterPeriod;
        $this->storeId = $storeId;
        $this->campaignId = $campaignId;
        $this->metric = $metric;
    }

    public static function create(
        LeaderboardId $leaderboardId,
        string $firstIterPeriod,
        string $previousIterPeriod,
        string $currentIterPeriod,
        StoreId $storeId,
        CampaignId $campaignId,
        Metric $metric
    ): self {
        return new self(
            $leaderboardId,
            $firstIterPeriod
            $previousIterPeriod,
            $currentIterPeriod,
            $storeId,
            $campaignId,
            $metric
        );
    }

    public function getLeaderboardId(): LeaderboardId
    {
        return $this->leaderboardId;
    }

    public function getFirstIterPeriod(): string
    {
        return $this->firstIterPeriod;
    }

    public function getPreciousIterPeriod(): string
    {
        return $this->preciousIterPeriod;
    }

    public function getCurrentIterPeriod(): string
    {
        return $this->currentIterPeriod;
    }

    public function getRecalculatedAt(): ?DateTimeImmutable
    {
        return $this->recalculatedAt;
    }

    public function setRecalculatedAt(DateTimeImmutable $recalculatedAt): void
    {
        $this->recalculatedAt = $recalculatedAt;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(DateTimeImmutable $completedAt): void
    {
        $this->completedAt = $completedAt;
    }

    public function isCompleted(): bool
    {
        return $this->completedAt !== null;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }

    public function getMetric(): Metric
    {
        return $this->metric;
    }
}
