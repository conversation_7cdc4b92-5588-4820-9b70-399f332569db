<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Entity;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Leaderboard\Domain\ValueObject\IterPeriod;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

class Leaderboard
{
    private LeaderboardId $leaderboardId;
    private IterPeriod $iterPeriod;
    private ?DateTimeImmutable $recalculatedAt = null;
    private ?DateTimeImmutable $completedAt = null;
    private StoreId $storeId;
    private Metric $metric;

    private function __construct(
        LeaderboardId $leaderboardId,
        IterPeriod $iterPeriod,
        StoreId $storeId,
        Metric $metric
    ) {
        $this->leaderboardId = $leaderboardId;
        $this->iterPeriod = $iterPeriod;
        $this->storeId = $storeId;
        $this->metric = $metric;
    }

    public static function create(
        LeaderboardId $leaderboardId,
        IterPeriod $iterPeriod,
        StoreId $storeId,
        Metric $metric
    ): self {
        return new self(
            $leaderboardId,
            $iterPeriod,
            $storeId,
            $metric
        );
    }

    public function getLeaderboardId(): LeaderboardId
    {
        return $this->leaderboardId;
    }

    public function getIterPeriod(): IterPeriod
    {
        return $this->iterPeriod;
    }

    public function getRecalculatedAt(): ?DateTimeImmutable
    {
        return $this->recalculatedAt;
    }

    public function setRecalculatedAt(DateTimeImmutable $recalculatedAt): void
    {
        $this->recalculatedAt = $recalculatedAt;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(DateTimeImmutable $completedAt): void
    {
        $this->completedAt = $completedAt;
    }

    public function isCompleted(): bool
    {
        return $this->completedAt !== null;
    }

    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }

    public function getMetric(): Metric
    {
        return $this->metric;
    }
}
