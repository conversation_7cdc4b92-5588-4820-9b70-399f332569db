<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Entity;

use DateTimeImmutable;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

class Leaderboard
{
    private LeaderboardId $leaderboardId;
    private string $iterPeriod;
    private string $periodFrom;
    private string $periodTo;
    private ?DateTimeImmutable $recalculatedAt = null;
    private ?DateTimeImmutable $completedAt = null;
    private StoreId $storeId;
    private CampaignId $campaignId;
    private Metric $metric;
    
    public function __construct(
        LeaderboardId $leaderboardId,
        string $iterPeriod,
        string $periodFrom,
        string $periodTo,
        StoreId $storeId,
        CampaignId $campaignId,
        Metric $metric
    ) {
        $this->leaderboardId = $leaderboardId;
        $this->iterPeriod = $iterPeriod;
        $this->periodFrom = $periodFrom;
        $this->periodTo = $periodTo;
        $this->storeId = $storeId;
        $this->campaignId = $campaignId;
        $this->metric = $metric;
    }
    
    public function getLeaderboardId(): LeaderboardId
    {
        return $this->leaderboardId;
    }
    
    public function getIterPeriod(): string
    {
        return $this->iterPeriod;
    }
    
    public function getPeriodFrom(): string
    {
        return $this->periodFrom;
    }
    
    public function getPeriodTo(): string
    {
        return $this->periodTo;
    }
    
    public function getRecalculatedAt(): ?DateTimeImmutable
    {
        return $this->recalculatedAt;
    }
    
    public function setRecalculatedAt(DateTimeImmutable $recalculatedAt): void
    {
        $this->recalculatedAt = $recalculatedAt;
    }
    
    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }
    
    public function setCompletedAt(DateTimeImmutable $completedAt): void
    {
        $this->completedAt = $completedAt;
    }
    
    public function isCompleted(): bool
    {
        return $this->completedAt !== null;
    }
    
    public function getStoreId(): StoreId
    {
        return $this->storeId;
    }
    
    public function getCampaignId(): CampaignId
    {
        return $this->campaignId;
    }
    
    public function getMetric(): Metric
    {
        return $this->metric;
    }
}
