<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain;

use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\StoreId;

interface LeaderboardGateInterface
{
    public function createLeaderboardForCampaign(
        CampaignId $campaignId,
        StoreId $storeId,
        \DateTimeInterface $startsAt,
        ?\DateTimeInterface $endsAt,
        Leaderboard $leaderboardConfig
    ): void;
}
