<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain;

use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Repository;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;

interface LeaderboardRepositoryInterface extends Repository
{
    public function save(Leaderboard $leaderboard): void;

    public function byId(LeaderboardId $leaderboardId): ?Leaderboard;
}
