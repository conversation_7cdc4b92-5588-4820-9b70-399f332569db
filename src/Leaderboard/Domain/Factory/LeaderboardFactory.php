<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Factory;

use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\Id\StoreId;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;
use OpenLoyalty\Leaderboard\Domain\Service\PeriodProviderInterface;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;
use DateTimeImmutable;

final readonly class LeaderboardFactory implements LeaderboardFactoryInterface
{
    public function __construct(
        private PeriodProviderInterface $periodProvider
    ) {
    }

    public function create(
        CampaignId $campaignId,
        StoreId $storeId,
        DateTimeImmutable $startsAt,
        ?DateTimeImmutable $endsAt,
        Metric $metric
    ): Leaderboard
    {
        $periodData = $this->periodProvider->calculatePeriods($startsAt, $endsAt, $metric);

        return Leaderboard::create(
            new LeaderboardId($campaignId->__toString()),
            $periodData,
            $storeId,
            $metric
        );
    }
}
