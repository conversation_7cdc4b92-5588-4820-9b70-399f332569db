<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Factory;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard as LeaderboardConfig;
use OpenLoyalty\Core\Domain\Id\LeaderboardId;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;

final readonly class LeaderboardFactory implements LeaderboardFactoryInterface
{
    public function __construct(private UuidGeneratorInterface $uuidGenerator)
    {
    }

    public function createFromCampaign(Campaign $campaign, LeaderboardConfig $leaderboardConfig): Leaderboard
    {
        $metric = new Metric(
            $leaderboardConfig->getMetric()->getType(),
            $leaderboardConfig->getMetric()->getWalletTypeCode()
        );

        return new Leaderboard(
            new LeaderboardId($this->uuidGenerator->generate()),
            'monthly', // iterPeriod - domyślnie miesięczny
            $campaign->getStartsAt()->format('Y-m-d'), // periodFrom
            $campaign->getEndsAt()?->format('Y-m-d') ?? '2099-12-31', // periodTo
            $campaign->getStore(),
            $campaign,
            $metric
        );
    }
}
