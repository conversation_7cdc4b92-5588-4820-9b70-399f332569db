<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Factory;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard as LeaderboardConfig;
use OpenLoyalty\Leaderboard\Domain\Entity\Leaderboard;

interface LeaderboardFactoryInterface
{
    public function createFromCampaign(Campaign $campaign, LeaderboardConfig $leaderboardConfig): Leaderboard;
}
