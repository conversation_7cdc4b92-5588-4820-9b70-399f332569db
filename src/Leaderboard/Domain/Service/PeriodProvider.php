<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Service;

use DateTimeInterface;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;
use OpenLoyalty\Leaderboard\Domain\ValueObject\IterPeriod;

final readonly class PeriodProvider implements PeriodProviderInterface
{
    public function calculatePeriods(
        DateTimeInterface $startsAt,
        ?DateTimeInterface $endsAt,
        Metric $metric
    ): IterPeriod {
        if ($metric->getType() === Metric::TYPE_EARNED_UNITS_CUMULATIVE) {
            return new IterPeriod('default', 'default', 'default');
        }

        throw new \InvalidArgumentException(sprintf('Unsupported metric type: %s', $metric->getType()));
    }
}
