<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Leaderboard\Domain\Service;

use DateTimeInterface;
use OpenLoyalty\Leaderboard\Domain\ValueObject\Metric;
use OpenLoyalty\Leaderboard\Domain\ValueObject\IterPeriod;

interface PeriodProviderInterface
{
    public function calculatePeriods(
        DateTimeInterface $startsAt,
        ?DateTimeInterface $endsAt,
        Metric $metric
    ): IterPeriod;
}
