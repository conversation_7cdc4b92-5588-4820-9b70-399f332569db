<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Ui\Rest\Controller;

use FOS\RestBundle\Controller\AbstractFOSRestController;
use FOS\RestBundle\Controller\Annotations\Route;
use FOS\RestBundle\View\View;
use OpenLoyalty\Campaign\Application\UseCase\CreateCampaignUseCase;
use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Effect\InvalidEffectException;
use OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignTypeException;
use OpenLoyalty\Campaign\Domain\Exception\PotentialInfiniteCampaignLoopException;
use OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException;
use OpenLoyalty\Campaign\Infrastructure\Form\Type\CreateCampaignFormType;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Id\CampaignId;
use OpenLoyalty\Core\Domain\Provider\StoreContextProviderInterface;
use OpenLoyalty\Core\Domain\UuidGeneratorInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

final class Post extends AbstractFOSRestController
{
    public function __construct(
        private readonly CreateCampaignUseCase $useCase,
        private readonly FormFactoryInterface $formFactory,
        private readonly StoreContextProviderInterface $storeContextProvider,
        private readonly TranslatorInterface $translator,
        private readonly UuidGeneratorInterface $uuidGenerator
    ) {
    }

    /**
     * @Route(methods={"POST"}, name="oloy.campaign.create", path="/{storeCode}/campaign")
     *
     * @Security("is_granted('CREATE_CAMPAIGN')")
     */
    public function __invoke(Request $request): View
    {
        $campaignId = new CampaignId($this->uuidGenerator->generate());
        dump($request->getContent());
        $form = $this->formFactory->createNamed(
            'campaign',
            CreateCampaignFormType::class,
            [],
            [
                'campaignId' => $campaignId,
                'store' => $this->storeContextProvider->getStore(),
                'validation_groups' => ['create', 'Default'],
            ]
        );
        $form->handleRequest($request);

        if (!$form->isSubmitted() || !$form->isValid()) {
            return $this->view($form, Response::HTTP_BAD_REQUEST);
        }

        try {
            $this->useCase->execute($form->getData());

            return $this->view(['campaignId' => (string) $campaignId]);
        } catch (InvalidConditionException) {
            $form->addError(new FormError($this->translator->trans('campaign.invalid_condition')));
        } catch (InvalidEffectException) {
            $form->addError(new FormError($this->translator->trans('campaign.invalid_effect')));
        } catch (InvalidCampaignTypeException) {
            $form->addError(new FormError($this->translator->trans('campaign.invalid_type')));
        } catch (InvalidTriggerException) {
            $form->addError(new FormError($this->translator->trans('campaign.invalid_trigger')));
        } catch (InvalidCampaignRuleException) {
            $form->addError(new FormError($this->translator->trans('campaign.invalid_rule')));
        } catch (PotentialInfiniteCampaignLoopException) {
            $form->addError(new FormError($this->translator->trans('campaign.potential_infinite_campaign_loop')));
        }

        return $this->view($form, Response::HTTP_BAD_REQUEST);
    }
}
