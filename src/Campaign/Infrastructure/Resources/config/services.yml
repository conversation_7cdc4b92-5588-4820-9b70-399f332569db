imports:
    - { resource: services/*.yml }

services:

    _defaults:
        autowire: true
        autoconfigure: true
        public: false
        bind:
            $visibilityCacheLifetime: '%campaign_visibility_query_cache_ttl%'

    _instanceof:
        OpenLoyalty\Core\Domain\Message\EventHandlerInterface:
            tags: [ { name: messenger.message_handler, bus: event.bus } ]
        OpenLoyalty\Core\Application\PublicEvent\Message\PublicEventHandlerInterface:
            tags: [ { name: messenger.message_handler, bus: public.event.bus } ]
        OpenLoyalty\Core\Domain\ReadModel\ProjectorInterface:
            tags: [ { name: readmodel.projector } ]
        OpenLoyalty\Core\Domain\ReadModel\ProjectionInitializerInterface:
            tags: [ { name: readmodel.projector_initializer } ]
        OpenLoyalty\Campaign\Domain\Effect\EffectAlgorithmInterface:
            tags: ['oloy.campaign.effect.algorithm']
        OpenLoyalty\Campaign\Domain\Effect\Calculation\EffectCalculatorInterface:
            tags: ['oloy.campaign.effect.calculator']
        OpenLoyalty\Campaign\Domain\Effect\Factory\EffectResultFactoryInterface:
            tags: [ 'oloy.campaign.effect.resultFactory' ]
        OpenLoyalty\Campaign\Domain\Simulation\Factory\SimulatedEffectResultFactoryInterface:
            tags: [ 'oloy.campaign.simulation.resultFactory' ]
        OpenLoyalty\Campaign\Domain\Limit\Validator\LimitValidatorInterface:
            tags: [ 'oloy.campaign.limit.validator' ]
        OpenLoyalty\Campaign\Domain\Limit\Validator\Effect\UnitsEffectLimitValidatorInterface:
            tags: [ 'oloy.campaign.effect.limit.validator' ]
        OpenLoyalty\Core\Domain\Webhook\DataProvider\WebhookDataProviderInterface:
            tags: [ 'oloy.webhook.data.provider' ]
        Sensio\Bundle\FrameworkExtraBundle\Request\ParamConverter\ParamConverterInterface:
            tags:
                - { name: 'request.param_converter', priority: '2' }
        OpenLoyalty\Core\Infrastructure\Message\FifoGroup\FifoGroupInterface:
            tags: ['oloy.core.message.fifogroup']
        OpenLoyalty\Core\Infrastructure\Message\Lock\LockItemPropertiesProviderInterface:
            tags: [ 'oloy.core.message.lock_item_properties_provider' ]
        OpenLoyalty\Campaign\Domain\TriggerStrategy\Strategy\TriggerStrategyInterface:
            tags: [ 'oloy.campaign.strategy.trigger_strategy' ]
        OpenLoyalty\Core\Domain\Condition\Schema\ContextFieldsSchemaProviderInterface:
            tags: [ 'oloy.condition.context_schema_provider' ]
        OpenLoyalty\Core\Infrastructure\Condition\ConditionFieldParamsFormProviderInterface:
            tags: [ 'oloy.condition.field_params_form_provider' ]
        OpenLoyalty\Core\Domain\DataRetention\DataRetentionHandlerInterface:
            tags: [ 'oloy.core.dataretention.handler' ]

    OpenLoyalty\Campaign\Domain\:
        resource: '../../../Domain'
        exclude:
            - '../../../Domain/{ValueObject,Exception,Entity}'
            - '../../../Domain/Context/Strategy'
            - '../../../Domain/Webhook/Response'

    OpenLoyalty\Campaign\Domain\Entity\Condition\ConditionBuilder: ~
    OpenLoyalty\Campaign\Domain\Entity\Effect\EffectBuilder: ~

    OpenLoyalty\Campaign\Application\UseCase\:
        resource: '../../../Application/UseCase'

    OpenLoyalty\Campaign\Application\DataMapper\:
        resource: '../../../Application/DataMapper'

    OpenLoyalty\Campaign\Application\JobHandler\:
        resource: '../../../Application/JobHandler'

    OpenLoyalty\Campaign\Application\Webhook\DataProvider\:
        resource: '../../../Application/Webhook/DataProvider'

    OpenLoyalty\Campaign\Infrastructure\:
        resource: '../../*'
        exclude:
            - '../../{DependencyInjection,Resources,OpenLoyaltyCampaignBundle.php}'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate\CustomerAddressSimulateFormType:
        arguments:
            $stringFieldMaxLength: '%form_string_field_max_length%'

    OpenLoyalty\Campaign\Infrastructure\Form\EventSubscriber\CampaignAdditionalFieldsSubscriber:
        arguments:
            $transactionItemsFiltersMax: '%campaign_transaction_items_filters_max%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate\CustomerSimulateFormType:
        arguments:
            $stringFieldMaxLength: '%form_string_field_max_length%'
            $numberMinValue: '%form_number_field_min_value%'
            $numberMaxValue: '%form_number_field_max_value%'
            $maxCustomAttributesInMember: '%max_custom_attributes_in_member%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate\TransactionItemSimulateFormType:
        arguments:
            $stringFieldMaxLength: '%form_string_field_max_length%'
            $numberMinValue: '%form_number_field_min_value%'
            $numberMaxValue: '%form_number_field_max_value%'
            $maxCustomAttributesInTransactionItem: '%max_custom_attributes_in_transaction_item%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate\TransactionSimulateFormType:
        arguments:
            $maxCustomAttributesInTransaction: '%max_custom_attributes_in_transaction%'
            $maxTransactionItems: '%max_items_in_transaction%'
            $stringFieldMaxLength: '%form_string_field_max_length%'
            $numberMinValue: '%form_number_field_min_value%'
            $numberMaxValue: '%form_number_field_max_value%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignConditionFormType:
        arguments:
            $maxCustomAttributesInCampaignSimulation: '%max_custom_attributes_in_campaign_simulation%'
            $numberMaxValue: '%form_number_field_max_value%'
            $stringFieldMaxLength: '%form_string_field_max_length%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\LabelFormType:
        arguments:
            $customAttributeKeyMaxLength: '%form_custom_attribute_key_max_length%'
            $customAttributeValueMaxLength: '%form_custom_attribute_value_max_length%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\Simulate\InternalEventBodyFormType:
        arguments:
            $maxCustomEventSchemaFields: '%max_fields_in_custom_event_schema%'
            $stringFieldMaxLength: '%form_string_field_max_length%'
            $numberMinValue: '%form_number_field_min_value%'
            $numberMaxValue: '%form_number_field_max_value%'

    OpenLoyalty\Campaign\Domain\Effect\EffectApplier:
        arguments: [!tagged_iterator { tag: 'oloy.campaign.effect.algorithm' }]

    OpenLoyalty\Campaign\Domain\Effect\Calculation\EffectCalculator:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.effect.calculator' } ]

    OpenLoyalty\Campaign\Domain\Effect\Factory\EffectResultFactory:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.effect.resultFactory' } ]

    OpenLoyalty\Campaign\Domain\Simulation\Factory\SimulatedEffectResultFactory:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.simulation.resultFactory' } ]

    OpenLoyalty\Campaign\Domain\Limit\Validator\Effect\EffectLimitValidator:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.effect.limit.validator' } ]

    OpenLoyalty\Campaign\Domain\Availability\CampaignChecker:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.limit.validator' } ]

    OpenLoyalty\Campaign\Domain\TriggerStrategy\TriggerStrategyExecutor:
        arguments: [ !tagged_iterator { tag: 'oloy.campaign.strategy.trigger_strategy' } ]

    OpenLoyalty\Campaign\Infrastructure\Search\CriteriaBuilder\Doctrine\:
        resource: '../../Search/CriteriaBuilder/Doctrine/*'
        tags:
            - { name: 'search.criteria.builder', context: 'doctrine/Campaign' }

    OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\ConditionValidValidator: ~

    OpenLoyalty\Campaign\Domain\TriggerStrategy\Validator\DailyCampaignTargetLimitValidatorInterface:
        class: OpenLoyalty\Campaign\Domain\TriggerStrategy\Validator\DailyCampaignTargetLimitValidator
        arguments:
            $targetLimit: '%daily_campaign_target_limit%'

    OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\DailyTypeTargetLimitValidValidator:
        arguments:
            $targetLimit: '%daily_campaign_target_limit%'

    OpenLoyalty\Campaign\Infrastructure\Validator\Constraint\TimeCampaignLimitValidValidator:
        arguments:
            $limit: '%time_campaign_limit%'

    OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignEffectFormType: ~

    OpenLoyalty\Campaign\Infrastructure\Form\Type\CampaignRuleFormType: ~

    OpenLoyalty\Campaign\Domain\Entity\Rule\RuleFactory: ~
    OpenLoyalty\Campaign\Domain\Entity\Rule\RuleFactoryInterface: '@OpenLoyalty\Campaign\Domain\Entity\Rule\RuleFactory'

    OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository: ~
    OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepositoryInterface: '@OpenLoyalty\InternalEvent\Domain\InternalEventSchemaRepository'

    OpenLoyalty\Campaign\Domain\CampaignRunner:
        arguments:
            $simulationEngine: '@OpenLoyalty\Campaign\Domain\Engine\SimulationEngine'
            $engine: '@OpenLoyalty\Campaign\Domain\Engine\Engine'

    OpenLoyalty\Campaign\Domain\Time\CampaignProcessorInterface: '@OpenLoyalty\Campaign\Infrastructure\Time\JobCampaignProcessor'

    OpenLoyalty\Campaign\Domain\CampaignRunnerInterface: '@OpenLoyalty\Campaign\Domain\CampaignRunner'
    OpenLoyalty\Campaign\Domain\SystemEvent\Listener\ProcessCampaignsCustomEventAssignedListener:
        arguments:
            $campaignRunner: '@OpenLoyalty\Campaign\Infrastructure\JobCampaignRunner'
    OpenLoyalty\Campaign\Domain\SystemEvent\Listener\ProcessCampaignsInternalEventAssignedListener:
        arguments:
            $campaignRunner: '@OpenLoyalty\Campaign\Infrastructure\JobCampaignRunner'
    OpenLoyalty\Campaign\Domain\SystemEvent\Listener\ProcessCampaignsTransactionAssignedListener:
        arguments:
            $campaignRunner: '@OpenLoyalty\Campaign\Infrastructure\JobCampaignRunner'

    OpenLoyalty\Campaign\Infrastructure\Message\FifoGroup\RunCampaignFifoGroup: ~

    OpenLoyalty\Campaign\Infrastructure\Message\FifoGroup\RunTimeCampaignFifoGroup: ~

    OpenLoyalty\Campaign\Application\Listener\RecreateMemberCampaignUsagesProjection: ~

    OpenLoyalty\Campaign\Application\Listener\RecreateCampaignProjection:
        arguments:
            $readModelCampaignRecreateDelayed: '%env(READMODEL_CAMPAIGN_RECREATE_TRIGGER_DELAYED)%'

    OpenLoyalty\Campaign\Application\Listener\PublishCampaignEffectWereApplied: ~

    OpenLoyalty\Campaign\Domain\ReadModel\Repository\CampaignReadModelRepositoryReadContextInterface:
        class: 'OpenLoyalty\Campaign\Infrastructure\Persistence\ReadModel\Repository\CampaignReadModelRepository'
        arguments:
            $entityManager: '@readmodel.entity_manager'

    OpenLoyalty\Campaign\Domain\ReadModel\Repository\MemberCampaignUsagesReadModelRepositoryReadContextInterface:
        class: 'OpenLoyalty\Campaign\Infrastructure\Persistence\ReadModel\Repository\MemberCampaignUsagesReadModelRepository'
        arguments:
            $entityManager: '@readmodel.entity_manager'

    OpenLoyalty\Campaign\Infrastructure\TierFacade: ~
    OpenLoyalty\Campaign\Domain\TierFacadeInterface: '@OpenLoyalty\Campaign\Infrastructure\TierFacade'

    OpenLoyalty\Campaign\Infrastructure\SegmentFacade: ~
    OpenLoyalty\Campaign\Domain\SegmentFacadeInterface: '@OpenLoyalty\Campaign\Infrastructure\SegmentFacade'

    OpenLoyalty\Campaign\Infrastructure\CampaignGate: ~
    OpenLoyalty\Campaign\Domain\CampaignGateInterface: '@OpenLoyalty\Campaign\Infrastructure\CampaignGate'

    OpenLoyalty\Campaign\Infrastructure\LeaderboardFacade: ~
    OpenLoyalty\Campaign\Domain\LeaderboardFacadeInterface: '@OpenLoyalty\Campaign\Infrastructure\LeaderboardFacade'

    OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityFactory: ~
    OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityFactoryInterface: '@OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityFactory'

    OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceFactory: ~
    OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceFactoryInterface: '@OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceFactory'

    OpenLoyalty\Campaign\Domain\CampaignExecutionsStatsRepositoryReadContextInterface:
        class: OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository\DoctrineCampaignExecutionsStatsRepository
        arguments:
            $entityManager: '@readmodel.entity_manager'

    OpenLoyalty\Campaign\Domain\CampaignRepositoryReadContextInterface:
        class: OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository\DoctrineCampaignRepository
        arguments:
            $entityManager: '@readmodel.entity_manager'

    OpenLoyalty\Campaign\Domain\CampaignExecutionRepositoryReadContextInterface:
        class: OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository\DoctrineCampaignExecutionRepository
        arguments:
            $entityManager: '@readmodel.entity_manager'

    OpenLoyalty\Campaign\Infrastructure\Message\Lock\RunCampaignLockItemPropertiesProvider:
        arguments:
            $lockTtl: '%messenger.short_lock_ttl%'

    OpenLoyalty\Campaign\Infrastructure\Message\Lock\RunTimeCampaignLockItemPropertiesProvider:
        arguments:
            $lockTtl: '%messenger.short_lock_ttl%'

    OpenLoyalty\Campaign\Domain\Checker\ActiveCampaignLimitChecker:
        arguments:
            $globalActiveCampaignLimit: '%global_active_campaign_limit%'

    OpenLoyalty\Campaign\Domain\Checker\ActiveRedemptionCodeLimitChecker:
        arguments:
            $globalRedemptionCodeLimit: '%global_campaign_redemption_code_limit%'


    OpenLoyalty\Campaign\Application\Webhook\Factory\RuleResponseFactory: ~
    OpenLoyalty\Campaign\Application\Webhook\Factory\LimitsResponseFactory: ~
    OpenLoyalty\Campaign\Application\Webhook\Factory\CodeGeneratorResponseFactory: ~
    OpenLoyalty\Campaign\Application\PublicEventHandler\TransferHasBeenCancelledHandler: ~

    OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler\CampaignExecutionHandler:
        arguments:
            $maxDeleteCount: '%data_retention_campaign_execution_max_delete_count%'
            $batchSize: '%data_retention_campaign_execution_batch_size%'

    OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler\CampaignCodeHandler:
        arguments:
            $maxDeleteCount: '%data_retention_campaign_code_max_delete_count%'
            $batchSize: '%data_retention_campaign_code_batch_size%'

    OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler\MemberCampaignUsagesReadModelHandler:
        arguments:
            $maxDeleteCount: '%data_retention_member_campaign_usages_max_delete_count%'
            $batchSize: '%data_retention_member_campaign_usages_batch_size%'

    OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler\CampaignTimeRequestHandler:
        arguments:
            $maxDeleteCount: '%data_retention_campaign_time_request_max_delete_count%'
            $batchSize: '%data_retention_campaign_time_request_batch_size%'
            $intervalDays: '%data_retention_campaign_time_request_interval_days%'

    OpenLoyalty\Campaign\Infrastructure\DataRetention\Handler\CampaignCalculatedEffectResultHandler:
        arguments:
            $maxDeleteCount: '%data_retention_campaign_calculated_effect_result_max_delete_count%'
            $batchSize: '%data_retention_campaign_calculated_effect_result_batch_size%'
            $intervalDays: '%data_retention_campaign_calculated_effect_result_interval_days%'

    OpenLoyalty\Campaign\Infrastructure\Persistence\Doctrine\Repository\DoctrineCampaignExecutionRepository: ~
