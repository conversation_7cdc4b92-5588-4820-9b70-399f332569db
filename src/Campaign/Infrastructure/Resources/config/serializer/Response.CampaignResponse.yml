OpenLoyalty\Campaign\Application\Response\CampaignResponse:
    exclusion_policy: ALL
    discriminator:
        disabled: true
    properties:
        campaignId:
            expose: true
            inline: true
        active:
            expose: true
            groups: ['admin']
        activity:
            expose: true
            groups: ['admin']
        rules:
            expose: true
            groups: ['admin']
        limits:
            expose: true
            groups: ['admin']
        translations:
            expose: true
        createdAt:
            expose: true
            groups: ['admin']
        memberFilter:
            expose: true
            groups: ['admin']
        displayOrder:
            expose: true
            groups: ['admin']
        labels:
            expose: true
            groups: ['admin']
        multiLevel:
            expose: true
            groups: ['admin']
        codeGenerator:
            expose: true
            groups: ['admin']
        eventCodeAttribute:
            expose: true
            groups: ['admin']
        name:
            expose: true
        description:
            expose: true
        type:
            expose: true
            groups: ['admin']
        trigger:
            expose: true
            groups: ['admin']
        event:
            expose: true
            groups: ['admin']
        achievementId:
            expose: true
            inline: true
            groups: ['admin']
        limitUsages:
            expose: true
        visibility:
            expose: true
            groups: [ 'admin' ]
        audience:
            expose: true
            groups: [ 'admin' ]
        triggerStrategy:
            expose: true
            groups: [ 'admin' ]
        executionSchedule:
            expose: true
            groups: [ 'admin' ]
        transactionItemsFilters:
            expose: true
            groups: [ 'admin' ]
        leaderboard:
            expose: true
            groups: ['admin']
