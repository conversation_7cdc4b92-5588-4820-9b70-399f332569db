<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure;

use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\LeaderboardFacadeInterface;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;
use OpenLoyalty\Leaderboard\Domain\LeaderboardGateInterface;

final readonly class LeaderboardFacade implements LeaderboardFacadeInterface
{
    public function __construct(private LeaderboardGateInterface $leaderboardGate)
    {
    }

    public function createLeaderboardForCampaign(Campaign $campaign, Leaderboard $leaderboardConfig): void
    {
        $this->leaderboardGate->createLeaderboardForCampaign(
            $campaign->getCampaignId(),
            $campaign->getStoreId(),
            $campaign->getActivity()->getStartsAt(),
            $campaign->getActivity()->getEndsAt(),
            $leaderboardConfig
        );
    }
}
