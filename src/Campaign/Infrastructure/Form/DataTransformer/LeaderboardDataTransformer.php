<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;
use OpenLoyalty\Campaign\Domain\ValueObject\Metric;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
final class LeaderboardDataTransformer implements DataTransformerInterface
{
    public function transform(mixed $value): ?array
    {
        if (!$value instanceof Leaderboard) {
            return null;
        }

        return [
            'metric' => $value->getMetric(),
        ];
    }

    public function reverseTransform(mixed $value): ?Leaderboard
    {
        if (!is_array($value) || !isset($value['metric']) || !$value['metric'] instanceof Metric) {
            return null;
        }

        return new Leaderboard($value['metric']);
    }
}
