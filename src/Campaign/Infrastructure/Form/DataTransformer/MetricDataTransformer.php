<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Infrastructure\Form\DataTransformer;

use OpenLoyalty\Campaign\Domain\ValueObject\Metric;
use Symfony\Component\Form\DataTransformerInterface;

/**
 * @implements DataTransformerInterface<mixed, mixed>
 */
final class MetricDataTransformer implements DataTransformerInterface
{
    public function transform(mixed $value): ?array
    {
        if (!$value instanceof Metric) {
            return null;
        }

        return [
            'type' => $value->type,
            'walletTypeCode' => $value->walletTypeCode,
        ];
    }

    public function reverseTransform(mixed $value): ?Metric
    {
        if (!isset($value['type'], $value['walletTypeCode']) || !is_array($value)) {
            return null;
        }

        return new Metric($value['type'], $value['walletTypeCode']);
    }
}
