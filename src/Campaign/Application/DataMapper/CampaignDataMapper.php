<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\DataMapper;

use OpenLoyalty\Campaign\Application\Response\CampaignResponse;
use OpenLoyalty\Campaign\Application\Response\LimitUsages;
use OpenLoyalty\Campaign\Application\Response\Usages;
use OpenLoyalty\Campaign\Domain\Campaign;
use OpenLoyalty\Campaign\Domain\LeaderboardFacadeInterface;
use OpenLoyalty\Campaign\Domain\ReadModel\Entity\Campaign as CampaignReadModel;
use OpenLoyalty\Campaign\Domain\ReadModel\Projector\SingleCampaignRequestProjection;
use OpenLoyalty\Core\Application\DataMapper\DataMapperInterface;
use OpenLoyalty\Core\Domain\ReadModel\ProjectionProviderInterface;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;

final readonly class CampaignDataMapper implements DataMapperInterface
{
    public function __construct(
        private ProjectionProviderInterface $projectionProvider,
        private TranslationsDataMapper $translationsDataMapper,
        private CampaignVisibilityDataMapper $campaignVisibilityDataMapper,
        private CampaignAudienceDataMapper $campaignAudienceDataMapper,
        private CampaignTriggerStrategyDataMapper $campaignTriggerStrategyDataMapper,
        private LeaderboardFacadeInterface $leaderboardFacade,
    ) {
    }

    public function map(Campaign $entity): CampaignResponse
    {
        /** @var CampaignReadModel $projection */
        $projection = $this->projectionProvider->getProjection(
            new SingleCampaignRequestProjection(
                $entity->getStoreId(),
                $entity->getCampaignId()
            )
        );

        $usedUnits = $projection->getUsedUnits();
        $limit = $entity->getLimits();

        $usageUnits = null;
        $limitValue = $limit?->getUnitsLimit()?->getValue();
        if (null !== $limitValue) {
            $usageUnits = new Usages(
                $usedUnits,
                $limitValue,
                max($limitValue - $usedUnits, 0),
                /* @phpstan-ignore-next-line */
                $limit?->getUnitsLimit()?->getInterval()
            );
        }

        return new CampaignResponse(
            $entity->getCampaignId(),
            $entity->isActive(),
            $entity->getActivity(),
            $entity->getRules(),
            $entity->getLimits(),
            $this->translationsDataMapper->map($entity->getTranslations()),
            $entity->getCreatedAt(),
            $entity->getMemberFilter(),
            $entity->getDisplayOrder(),
            $entity->getLabels(),
            $entity->getMultiLevel(),
            $entity->getCodeGenerator(),
            $entity->getEventCodeAttribute(),
            $entity->getName(),
            $entity->getDescription(),
            $entity->getType()->getCode(),
            $entity->getTrigger()->getCode(),
            $entity->getEvent(),
            $entity->getAchievementId(),
            new LimitUsages(
                $usageUnits,
                null,
                null
            ),
            $this->campaignVisibilityDataMapper->map($entity),
            $this->campaignAudienceDataMapper->map($entity),
            $this->campaignTriggerStrategyDataMapper->map($entity->getTriggerStrategy()),
            $entity->getTransactionItemsFilters(),
            $entity->getTriggerCode() === Trigger::LEADERBOARD ? $this->leaderboardFacade->getLeaderboardForCampaign($entity->getCampaignId()) : null
        );
    }

    /**
     * @param Campaign[] $entities
     *
     * @return CampaignResponse[]
     */
    public function mapList(array $entities): array
    {
        $campaigns = [];
        foreach ($entities as $entity) {
            $campaigns[] = $this->map($entity);
        }

        return $campaigns;
    }
}
