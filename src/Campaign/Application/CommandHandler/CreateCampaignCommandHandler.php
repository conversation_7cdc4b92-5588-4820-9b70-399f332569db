<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\CommandHandler;

use Assert\AssertionFailedException;
use OpenLoyalty\Campaign\Application\Command\CreateCampaign;
use OpenLoyalty\Campaign\Application\Job\CreateCampaignCodesJob;
use OpenLoyalty\Campaign\Domain\CampaignRepositoryInterface;
use OpenLoyalty\Campaign\Domain\Condition\InvalidConditionException;
use OpenLoyalty\Campaign\Domain\Effect\InvalidEffectException;
use OpenLoyalty\Campaign\Domain\Entity\Audience\CampaignAudienceFactoryInterface;
use OpenLoyalty\Campaign\Domain\Entity\Visibility\CampaignVisibilityFactoryInterface;
use OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignEventTypeException;
use OpenLoyalty\Campaign\Domain\Exception\InvalidCampaignTypeException;
use OpenLoyalty\Campaign\Domain\Exception\InvalidMultiLevelException;
use OpenLoyalty\Campaign\Domain\Factory\CampaignFactoryInterface;
use OpenLoyalty\Campaign\Domain\Rule\InvalidCampaignRuleException;
use OpenLoyalty\Campaign\Domain\SystemEvent\CampaignWasCreated;
use OpenLoyalty\Core\Application\Exception\InvalidStoreException;
use OpenLoyalty\Core\Domain\Exception\InvalidTriggerException;
use OpenLoyalty\Core\Domain\Message\CommandHandlerInterface;
use OpenLoyalty\Core\Domain\Message\EventBusInterface;
use OpenLoyalty\Core\Domain\Message\JobBusInterface;
use OpenLoyalty\Core\Domain\Store;
use OpenLoyalty\Core\Domain\StoreRepository;
use OpenLoyalty\Core\Domain\ValueObject\Trigger;
use OpenLoyalty\Campaign\Domain\LeaderboardFacadeInterface;

final readonly class CreateCampaignCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private CampaignRepositoryInterface $campaignRepository,
        private StoreRepository $storeRepository,
        private CampaignFactoryInterface $campaignFactory,
        private JobBusInterface $jobBus,
        private CampaignVisibilityFactoryInterface $campaignVisibilityFactory,
        private CampaignAudienceFactoryInterface $campaignAudienceFactory,
        private EventBusInterface $eventBus,
        private LeaderboardFacadeInterface $leaderboardFacade
    ) {
    }

    /**
     * @throws InvalidConditionException
     * @throws InvalidEffectException
     * @throws InvalidCampaignTypeException
     * @throws InvalidTriggerException
     * @throws InvalidCampaignEventTypeException
     * @throws InvalidCampaignRuleException
     * @throws InvalidMultiLevelException
     * @throws AssertionFailedException
     * @throws InvalidStoreException
     */
    public function __invoke(CreateCampaign $command): void
    {
        $store = $this->storeRepository->byId($command->getStoreId());

        if (!$store instanceof Store) {
            throw new InvalidStoreException();
        }

        $campaign = $this->campaignFactory->create(
            $command->getCampaignId(),
            $store,
            $command->getType(),
            $command->getTrigger(),
            $command->getStartsAt(),
            $command->getEndsAt(),
            $command->getRules(),
            $command->getLimits(),
            $command->getTriggerStrategyType(),
            $command->getLabels(),
            $command->getCodeGenerator(),
            $command->getEventCodeAttribute(),
            $command->getExecutionSchedule(),
        );

        if ($command->isActive()) {
            $campaign->activate();
        }

        $campaign->assignTranslations($command->getTranslations());

        if (null !== $command->getEvent()) {
            $campaign->assignEvent($command->getEvent());
        }

        if (null !== $command->getMemberFilter()) {
            $campaign->assignMemberFilter($command->getMemberFilter());
        }

        if (null !== $command->getAchievementId()) {
            $campaign->assignAchievementId($command->getAchievementId());
        }

        if (null !== $command->getMultiLevel()) {
            $campaign->enableMultiLevel($command->getMultiLevel());
        } else {
            $campaign->disableMultiLevel();
        }

        if (null !== $command->getTransactionItemsFilters()) {
            $campaign->setTransactionItemsFilters($command->getTransactionItemsFilters());
        }

        if (Trigger::CUSTOM_EVENT_UNIQUE_CODE === $command->getTrigger()) {
            $this->jobBus->dispatch(
                new CreateCampaignCodesJob(
                    $command->getStoreId(),
                    $command->getGenerateCodes(),
                    $command->getCampaignId()
                )
            );
        }

        $campaign->setDisplayOrder($command->getDisplayOrder());

        $campaign->changeVisibility(
            $this->campaignVisibilityFactory->create(
                $campaign,
                $campaign->getStoreId(),
                $command->getVisibility()
            )
        );

        $campaign->changeAudience(
            $this->campaignAudienceFactory->create(
                $campaign,
                $campaign->getStoreId(),
                $command->getAudience()
            )
        );

        $this->campaignRepository->save($campaign);

        if (null !== $command->getLeaderboard()) {
            $this->leaderboardFacade->createLeaderboardForCampaign(
                $campaign,
                $command->getLeaderboard());
        }

        $this->eventBus->dispatch(new CampaignWasCreated($campaign));
    }
}
