<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Application\Response;

use DateTimeImmutable;
use OpenLoyalty\Campaign\Application\Response\LeaderboardResponse;
use OpenLoyalty\Campaign\Domain\ValueObject\Activity;
use OpenLoyalty\Campaign\Domain\ValueObject\CodeGenerator;
use OpenLoyalty\Campaign\Domain\ValueObject\Limits;
use OpenLoyalty\Campaign\Domain\ValueObject\MemberFilter;
use OpenLoyalty\Campaign\Domain\ValueObject\TransactionItemsFilter;
use OpenLoyalty\Core\Domain\Id\AchievementId;
use OpenLoyalty\Core\Domain\Id\CampaignId;

final readonly class CampaignResponse
{
    public function __construct(
        public CampaignId $campaignId,
        public bool $active,
        public Activity $activity,
        public array $rules,
        public ?Limits $limits,
        public array $translations,
        public DateTimeImmutable $createdAt,
        public ?MemberFilter $memberFilter,
        public ?int $displayOrder,
        public array $labels,
        public ?int $multiLevel,
        public ?CodeGenerator $codeGenerator,
        public ?string $eventCodeAttribute,
        public ?string $name,
        public ?string $description,
        public string $type,
        public string $trigger,
        public ?string $event,
        public ?AchievementId $achievementId,
        public ?LimitUsages $limitUsages,
        public ?CampaignVisibility $visibility,
        public ?CampaignAudience $audience,
        public ?TriggerStrategyResponse $triggerStrategy,
        /** @var TransactionItemsFilter[] */
        public ?array $transactionItemsFilters
    ) {
    }
}
