<?php
/*
 * Copyright © Open Loyalty, Inc. All rights reserved.
 * See LICENSE for license details.
 */

declare(strict_types=1);

namespace OpenLoyalty\Campaign\Domain;

use OpenLoyalty\Campaign\Application\Response\LeaderboardResponse;
use OpenLoyalty\Campaign\Domain\ValueObject\Leaderboard;
use OpenLoyalty\Core\Domain\Id\CampaignId;

interface LeaderboardFacadeInterface
{
    public function createLeaderboardForCampaign(
        Campaign $campaign,
        Leaderboard $leaderboardConfig
    ): void;

    public function getLeaderboardForCampaign(CampaignId $campaignId): ?LeaderboardResponse;
}
